# Issue Guidelines

## Reporting bugs

If you found a bug, report an issue and describe what's the expected behavior versus what actually happens. If the bug causes a crash, attach a full backtrace. If possible, a reproduction script showing the problem is highly appreciated.

## Reporting feature requests

Report a feature request **only after discussing it first on [discourse.dry-rb.org](https://discourse.dry-rb.org)** where it was accepted. Please provide a concise description of the feature.

## Reporting questions, support requests, ideas, concerns etc.

**PLEASE DON'T** - use [discourse.dry-rb.org](https://discourse.dry-rb.org) instead.

# Pull Request Guidelines

A Pull Request will only be accepted if it addresses a specific issue that was reported previously, or fixes typos, mistakes in documentation etc.

Other requirements:

1) Do not open a pull request if you can't provide tests along with it. If you have problems writing tests, ask for help in the related issue.
2) Follow the style conventions of the surrounding code. In most cases, this is standard ruby style.
3) Add API documentation if it's a new feature
4) Update API documentation if it changes an existing feature
5) Bonus points for sending a PR which updates user documentation in the `docsite` directory

# Asking for help

If these guidelines aren't helpful, and you're stuck, please post a message on [discourse.dry-rb.org](https://discourse.dry-rb.org).
