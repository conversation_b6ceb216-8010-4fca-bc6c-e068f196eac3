
<!--- DO NOT EDIT THIS FILE - IT'S AUTOMATICALLY GENERATED VIA DEVTOOLS --->

<% if latest_release.summary %>
<%= latest_release.summary %>

<% end %>

<% if latest_release.added? %>
### Added

<% latest_release.added.each do |log| %>
- <%= log %>
<% end %>

<% end %>
<% if latest_release.fixed? %>
### Fixed

<% latest_release.fixed.each do |log| %>
- <%= log %>
<% end %>

<% end %>
<% if latest_release.changed? %>
### Changed

<% latest_release.changed.each do |log| %>
- <%= log %>
<% end %>
<% end %>
<% if previous_release %>
<% ver_range = "v#{previous_release.version}...v#{latest_release.version}" %>

[Compare <%=ver_range%>](https://github.com/dry-rb/<%= project.name %>/compare/<%=ver_range%>)
<% end %>
