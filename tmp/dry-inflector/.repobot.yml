###########################################################
# DO NOT EDIT THIS FILE
#
# This is a config synced from dry-rb/template-gem repo
###########################################################

sources:
  - repo: dry-rb/template-gem
    sync:
      - ".repobot.yml.erb"
      - ".devtools/templates/*.sync:${{dir}}/${{name}}"
      - ".github/**/*.*"
      - ".rspec"
      - ".rubocop.yml"
      - "gemspec.erb:dry-inflector.gemspec"
      - "spec/support/*"
      - "CODE_OF_CONDUCT.md"
      - "CONTRIBUTING.md"
      - "LICENSE.erb"
      - "README.md.erb"
      - "Gemfile.devtools"
  - repo: repobot-app/workflows
    sync:
      - ".github/workflows/*.yml"
