# This file is synced from dry-rb/template-gem repo
name: CI

on:
  push:
    paths:
      - ".github/workflows/ci.yml"
      - "lib/**"
      - "*.gemspec"
      - "spec/**"
      - "Rakefile"
      - "Gemfile"
      - "Gemfile.devtools"
      - ".rubocop.yml"
      - "project.yml"
  pull_request:
    branches:
      - main
  create:

jobs:
  tests:
    runs-on: ubuntu-latest
    name: Tests
    strategy:
      fail-fast: false
      matrix:
        ruby:
        - "3.4"
        - "3.3"
        - "3.2"
        - "3.1"
        include:
          - ruby: "3.4"
            coverage: "true"
    env:
      COVERAGE: ${{matrix.coverage}}
      COVERAGE_TOKEN: ${{secrets.CODACY_PROJECT_TOKEN}}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Install package dependencies
        run: "[ -e $APT_DEPS ] || sudo apt-get install -y --no-install-recommends $APT_DEPS"
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{matrix.ruby}}
          bundler-cache: true
      - name: Run all tests
        run: bundle exec rake
  release:
    runs-on: ubuntu-latest
    if: contains(github.ref, 'tags') && github.event_name == 'create'
    needs: tests
    env:
      GITHUB_LOGIN: dry-bot
      GITHUB_TOKEN: ${{secrets.GH_PAT}}
    steps:
      - uses: actions/checkout@v3
      - name: Install package dependencies
        run: "[ -e $APT_DEPS ] || sudo apt-get install -y --no-install-recommends $APT_DEPS"
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.4
      - name: Install dependencies
        run: gem install ossy --no-document
      - name: Trigger release workflow
        run: |
          tag=$(echo $GITHUB_REF | cut -d / -f 3)
          ossy gh w dry-rb/devtools release --payload "{\"tag\":\"$tag\",\"sha\":\"${{github.sha}}\",\"tag_creator\":\"$GITHUB_ACTOR\",\"repo\":\"$GITHUB_REPOSITORY\",\"repo_name\":\"${{github.event.repository.name}}\"}"
