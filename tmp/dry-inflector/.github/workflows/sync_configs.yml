# This file is synced from dry-rb/template-gem repo

name: sync

on:
  repository_dispatch:
  push:
    branches:
      - "main"

jobs:
  main:
    runs-on: ubuntu-latest
    if: (github.event_name == 'repository_dispatch' && github.event.action == 'sync_configs') || github.event_name != 'repository_dispatch'
    env:
      GITHUB_LOGIN: dry-bot
      GITHUB_TOKEN: ${{ secrets.GH_PAT }}
    steps:
      - name: Checkout ${{github.repository}}
        uses: actions/checkout@v3
      - name: Checkout devtools
        uses: actions/checkout@v3
        with:
          repository: dry-rb/devtools
          path: tmp/devtools
      - name: Setup git user
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "dry-bot"
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: "3.1"
      - name: Install dependencies
        run: gem install ossy --no-document
      - name: Update changelog.yml from commit
        run: tmp/devtools/bin/update-changelog-from-commit $GITHUB_SHA
      - name: Compile CHANGELOG.md
        run: tmp/devtools/bin/compile-changelog
      - name: Commit
        run: |
          git add -A
          git commit -m "[devtools] sync" || echo "nothing to commit"
      - name: Push changes
        run: |
          git pull --rebase origin main
          git push https://dry-bot:${{secrets.GH_PAT}}@github.com/${{github.repository}}.git HEAD:main
