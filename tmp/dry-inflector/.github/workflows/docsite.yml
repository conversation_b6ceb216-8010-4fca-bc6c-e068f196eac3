# This file is synced from dry-rb/template-gem repo

name: docsite

on:
  push:
    paths:
      - docsite/**
      - .github/workflows/docsite.yml
    branches:
      - main
      - release-**
    tags:

jobs:
  update-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - run: |
          git fetch --no-tags --prune --depth=1 origin +refs/heads/*:refs/remotes/origin/*
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: "3.0.5"
      - name: Set up git user
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "dry-bot"
      - name: Install dependencies
        run: gem install ossy --no-document
      - name: Update release branches
        run: |
          branches=`git log --format=%B -n 1 $GITHUB_SHA | grep "docsite:release-" || echo "nothing"`

          if [[ ! $branches -eq "nothing" ]]; then
            for b in $branches
            do
              name=`echo $b | ruby -e 'puts gets[/:(.+)/, 1].gsub(/\s+/, "")'`

              echo "merging $GITHUB_SHA to $name"

              git checkout -b $name --track origin/$name

              echo `git log -n 1`

              git cherry-pick $GITHUB_SHA -m 1
            done

            git push --all "https://dry-bot:${{secrets.GH_PAT}}@github.com/$GITHUB_REPOSITORY.git"

            git checkout main
          else
            echo "no need to update branches"
          fi
      - name: Trigger dry-rb.org deploy
        env:
          GITHUB_LOGIN: dry-bot
          GITHUB_TOKEN: ${{secrets.GH_PAT}}
        run: ossy github workflow dry-rb/dry-rb.org ci
