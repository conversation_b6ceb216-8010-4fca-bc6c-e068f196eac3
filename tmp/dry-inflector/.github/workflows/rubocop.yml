# frozen_string_literal: true

# This file is synced from dry-rb/template-gem repo

name: RuboCop

on: [push, pull_request]

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      BUNDLE_ONLY: tools

    steps:
    - uses: actions/checkout@v3

    - name: Set up Ruby 3.2
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: 3.2
        bundler-cache: true

    - name: Run RuboCop
      run: bundle exec rubocop --parallel
