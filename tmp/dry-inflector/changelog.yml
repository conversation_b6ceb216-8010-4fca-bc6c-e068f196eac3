---
- version: 1.2.0
  summary:
  date: 2025-01-04
  changed:
    - "Bumped required Ruby version to 3.1 (@flash-gordon)"
- version: 1.1.0
  summary:
  date: 2024-07-02
  added:
    - |-
      Added "DB" as a default acronym (#49) (@timriley)
  fixed:
    - |-
      Fix incorrect inflections on words separated by spaces, underscores or hyphens (#47) (@parndt)
- version: 1.0.0
  summary:
  date: 2022-11-04
  changed:
    - "Bumped version to 1.0.0 (@solnic)"
- version: 0.3.0
  summary:
  date: "2022-07-12"
  added:
    - "Add CSV as default acronym (via #43) (@waiting-for-dev)"
  changed:
    - "Extra dashes are now omitted when converting to camelcase (via #40) (@postmodern)"
- version: 0.2.1
  summary:
  date: "2021-06-30"
  fixed:
    - "Fix singularizing -us suffix (issue #33 via #38) (@cllns)"
  added:
    - "Add default acronyms: API and CSRF (#35) (@jodosha)"
  changed:
- version: 0.2.0
  date: "2019-10-13"
  added:
    - '[A<PERSON><PERSON> & <PERSON><PERSON><PERSON>] Introduced `Dry::Inflector#camelize_upper`
      and `Dry::Inflector#camelize_lower`. `Dry::Inflector#camelize` is now an alias
      for `Dry::Inflector#camelize_upper`.
      ```ruby
      inflector.camelize_upper("data_mapper") # => "DataMapper"''
      inflector.camelize_lower("data_mapper") # => "dataMapper"''
      ```'
  fixed:
    - '[ecnal] Fixed singularization rules for words like "alias" or "status"'
- version: 0.1.2
  date: "2018-04-25"
  added:
    - "[Gustavo Caso & Nikita Shilnikov] Added support for acronyms"
- version: 0.1.1
  date: "2017-11-18"
  fixed:
    - "[Luca Guidi & Abinoam P. Marques Jr.] Ensure `Dry::Inflector#ordinalize` to work
      for all the numbers from 0 to 100"
- version: 0.1.0
  date: "2017-11-17"
  added:
    - "[Luca Guidi] Introduced `Dry::Inflector#pluralize`"
    - "[Luca Guidi] Introduced `Dry::Inflector#singularize`"
    - "[Luca Guidi] Introduced `Dry::Inflector#camelize`"
    - "[Luca Guidi] Introduced `Dry::Inflector#classify`"
    - "[Luca Guidi] Introduced `Dry::Inflector#tableize`"
    - "[Luca Guidi] Introduced `Dry::Inflector#dasherize`"
    - "[Luca Guidi] Introduced `Dry::Inflector#underscore`"
    - "[Luca Guidi] Introduced `Dry::Inflector#demodulize`"
    - "[Luca Guidi] Introduced `Dry::Inflector#humanize`"
    - "[Luca Guidi] Introduced `Dry::Inflector#ordinalize`"
    - "[Abinoam P. Marques Jr.] Introduced `Dry::Inflector#foreign_key`"
    - "[Abinoam P. Marques Jr.] Introduced `Dry::Inflector#constantize`"
